package com.jz.votesystem.answer;

import java.util.*;
import java.util.stream.Collectors;

import com.jz.votesystem.bean.VoteResult;
import com.jz.votesystem.bean.VotePaper;

/**
 * 【重要提醒】<br>
 * 在当前类或当前answer包中，实现相关逻辑<br>
 * 
 * 嘉宾人气投票活动服务
 */
public class VoteSystemService {

	/**
	 * 输入的投票原始信息
	 */
	private List<String> votePaperLines;

	/**
	 * 获取嘉宾人气投票活动服务实例
	 * @param votePaperLines 投票原始记录，格式为：{候选人ID}\t{提交时间}\t{VIP标识}\t{投票账号}
	 * 						（输入内容格式合规，不需要校验）
	 * @return 嘉宾人气投票活动服务实例
	 */
	public static VoteSystemService build(List<String> votePaperLines) {
		// 初始化服务实例
		VoteSystemService voteSystemService = new VoteSystemService(votePaperLines);
		// [可选]数据统一初始化处理
		voteSystemService.postConstruct();
		// 返回服务实例
		return voteSystemService;
	}

	/**
	 * 服务实例构造器
	 * 
	 * @param votePaperLines 投票原始信息行
	 */
	private VoteSystemService(List<String> votePaperLines) {
		this.votePaperLines = votePaperLines;
	}

	/**
	 * 【可选】自定义数据初始化
	 */
	public void postConstruct() {
		// TODO [可选]可以在这边解析votePaperLines属性的内容。并针对答题的数据处理需求，构造一些自定义的属性或对象
	}

	/**
	 * 第1问：答题位置
	 * @return 返回计票结果（不用排序）
	 */
	public List<VoteResult> answer1() {
		
		// TODO 作答第1问位置
		
		return null;
	}

	/**
	 * 第2问：答题位置
	 * @return 返回计票结果（不用排序）
	 */
	public List<VoteResult> answer2() {

		// TODO 作答第2问位置

		return null;
	}

	/**
	 * 第3问：答题位置
	 * @return 返回计票结果（不用排序）
	 */
	public List<VoteResult> answer3() {

		// TODO 作答第3问位置

		return null;
	}

	/**
	 * 第4问：答题位置
	 * @return 返回计票结果（不用排序）
	 */
	public List<VoteResult> answer4() {

		// TODO 作答第4问位置

		return null;
	}

}
