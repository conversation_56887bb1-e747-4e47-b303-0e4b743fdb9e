package com.jz.votesystem.bean;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 投票记录Bean类
 */
public class VotePaper {
    
    /**
     * 候选人ID
     */
    private int candidateId;
    
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    
    /**
     * VIP标识：P-普通账号，VIP-VIP账号，SVIP-超级VIP账号
     */
    private String vipType;
    
    /**
     * 投票账号
     */
    private String userId;
    
    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 构造函数
     */
    public VotePaper(int candidateId, LocalDateTime submitTime, String vipType, String userId) {
        this.candidateId = candidateId;
        this.submitTime = submitTime;
        this.vipType = vipType;
        this.userId = userId;
    }
    
    /**
     * 从输入行解析投票记录
     * @param line 输入行，格式：候选人ID\t提交时间\tVIP标识\t投票账号
     * @return 投票记录对象
     */
    public static VotePaper fromLine(String line) {
        String[] parts = line.split("\t");
        int candidateId = Integer.parseInt(parts[0]);
        LocalDateTime submitTime = LocalDateTime.parse(parts[1], FORMATTER);
        String vipType = parts[2];
        String userId = parts[3];
        return new VotePaper(candidateId, submitTime, vipType, userId);
    }
    
    /**
     * 获取标准投票周期标识（yyyy-MM-dd格式）
     * @return 投票周期标识
     */
    public String getStandardVoteCycle() {
        return submitTime.toLocalDate().toString();
    }
    
    /**
     * 获取自定义投票周期标识（10:00:00到次日09:59:59）
     * @return 投票周期标识
     */
    public String getCustomVoteCycle() {
        LocalDateTime cycleStart;
        if (submitTime.getHour() >= 10) {
            // 当天10点之后，周期从当天10点开始
            cycleStart = submitTime.toLocalDate().atTime(10, 0);
        } else {
            // 当天10点之前，周期从前一天10点开始
            cycleStart = submitTime.toLocalDate().minusDays(1).atTime(10, 0);
        }
        return cycleStart.toLocalDate().toString();
    }
    
    /**
     * 获取VIP等级对应的最大投票数
     * @return 最大投票数
     */
    public int getMaxVotes() {
        switch (vipType) {
            case "P":
                return 1;
            case "VIP":
                return 2;
            case "SVIP":
                return 3;
            default:
                return 1;
        }
    }
    
    // Getter和Setter方法
    public int getCandidateId() {
        return candidateId;
    }
    
    public void setCandidateId(int candidateId) {
        this.candidateId = candidateId;
    }
    
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }
    
    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    
    public String getVipType() {
        return vipType;
    }
    
    public void setVipType(String vipType) {
        this.vipType = vipType;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "VotePaper{" +
                "candidateId=" + candidateId +
                ", submitTime=" + submitTime +
                ", vipType='" + vipType + '\'' +
                ", userId='" + userId + '\'' +
                '}';
    }
}
