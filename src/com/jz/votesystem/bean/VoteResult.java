package com.jz.votesystem.bean;

/**
 * 1. 不要修改这个类，避免影响判卷！
 * 2. 所有作答内容在answer包中实现
 * 
 * 选票结果信息（打印结果用）
 */
public class VoteResult {

	/**
	 * 候选人ID
	 */
	private int candidateId;

	/**
	 * 获得选票数
	 */
	private int votingPaperNum;

	/**
	 * 带参构造器
	 * 
	 * @param candidateId    候选人ID
	 * @param votingPaperNum 计票结果
	 */
	public VoteResult(int candidateId, int votingPaperNum) {
		super();
		this.candidateId = candidateId;
		this.votingPaperNum = votingPaperNum;
	}

	/**
	 * 默认构造器
	 */
	public VoteResult() {
		super();
	}

	/**
	 * 打印结果用到的方法
	 */
	@Override
	public String toString() {
		return "candidateId: " + candidateId + ", votingPaperNum: " + votingPaperNum;
	}
	
	public int getCandidateId() {
		return candidateId;
	}

	public void setCandidateId(int candidateId) {
		this.candidateId = candidateId;
	}

	public int getVotingPaperNum() {
		return votingPaperNum;
	}

	public void setVotingPaperNum(int votingPaperNum) {
		this.votingPaperNum = votingPaperNum;
	}

}
