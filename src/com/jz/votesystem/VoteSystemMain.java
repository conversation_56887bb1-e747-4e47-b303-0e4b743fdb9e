package com.jz.votesystem;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Comparator;
import java.util.List;

import com.jz.votesystem.answer.VoteSystemService;
import com.jz.votesystem.bean.VoteResult;

/**
 * 【重要提醒】
 * 1. 不要修改这个类
 * 2. 所有作答内容在answer包中实现
 * 
 * 嘉宾人气投票活动系统主入口
 */
public class VoteSystemMain {

	public static void main(String[] args) throws IOException {

		// 按行读取原始票选信息
		// 输入内容格式合规，不需要校验
		List<String> votePaperLines = Files.readAllLines(new File("vote_paper.input").toPath());

		// 获取嘉宾人气投票活动服务实例
		VoteSystemService voteSystemService = VoteSystemService.build(votePaperLines);

		// 作答第1问
		List<VoteResult> resultList1 = voteSystemService.answer1();
		// 按候选人ID排序并打印结果
		sortAndPrintResult("Result - 1", resultList1);

		// 作答第2问
		List<VoteResult> resultList2 = voteSystemService.answer2();
		// 按候选人ID排序并打印结果
		sortAndPrintResult("Result - 2", resultList2);

		// 作答第3问
		List<VoteResult> resultList3 = voteSystemService.answer3();
		// 按候选人ID排序并打印结果
		sortAndPrintResult("Result - 3", resultList3);

		// 作答第4问
		List<VoteResult> resultList4 = voteSystemService.answer4();
		// 按候选人ID排序并打印结果
		sortAndPrintResult("Result - 4", resultList4);
	}

	/**
	 * 按候选人ID排序并打印结果
	 * 
	 * @param resultTitle 打印标题
	 * @param resultList  结果列表
	 */
	public static void sortAndPrintResult(String resultTitle, List<VoteResult> resultList) {
		// 打印结果标题
		System.out.println("[" + resultTitle + "]");
		System.out.println("---------------------");
		// 非空校验
		if (null != resultList && !resultList.isEmpty()) {
			// 按候选人ID排序
			resultList.sort(Comparator.comparingInt(VoteResult::getCandidateId));
			// 打印结果
			resultList.forEach(System.out::println);
		} else {
			System.out.println("null or empty: " + resultList);
		}
		// 结尾间隔
		System.out.println();
	}

}